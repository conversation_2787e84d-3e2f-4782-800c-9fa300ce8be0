# E-commerce Session Value Prediction Pipeline
## BTK Datathon 2025

This repository contains a complete machine learning pipeline for predicting session values from e-commerce interaction data.

## 🎯 Problem Statement

Given raw event-level e-commerce interaction data per session, predict the `session_value` (regression problem) for each session in the test set.

## 📊 Data Overview

- **Training Data**: 141,219 events across 70,736 sessions
- **Test Data**: 62,951 events across 30,789 sessions
- **Event Types**: VIEW, ADD_CART, REMOVE_CART, BUY
- **Target Variable**: session_value (highly skewed, range: 5.38 - 2328.66)

## 🚀 Pipeline Overview

### 1. Data Preprocessing & Cleaning ✅
- Loaded train.csv and test.csv
- Parsed datetime and categorical features
- No missing values found
- Identified high target skewness (7.99) requiring log transformation

### 2. Feature Engineering (Session-Level Aggregation) ✅
Created 27 features per session:
- **Event Counts**: total_events, num_views, num_add_cart, num_remove_cart, num_purchases
- **Unique Counts**: unique_products, unique_categories
- **Binary Flags**: has_purchase, has_view, has_add_cart, has_remove_cart
- **Conversion Metrics**: conversion_rate, cart_conversion_rate, cart_abandonment_rate
- **Event Ratios**: view_ratio, add_cart_ratio, remove_cart_ratio, purchase_ratio
- **Diversity Metrics**: products_per_event, categories_per_event
- **Time Features**: session_duration_minutes, hour_of_day, day_of_week, is_weekend
- **Categorical Encodings**: most_freq_category_encoded, most_freq_product_encoded, user_id_encoded

### 3. Model Training ✅
- **Algorithm**: LightGBM (Gradient Boosting)
- **Cross-Validation**: 5-fold GroupKFold (grouped by user_id to prevent leakage)
- **Target Transformation**: Log transformation due to high skewness
- **Performance**: RMSE = 19.66 ± 7.41
- **Early Stopping**: Prevented overfitting

### 4. Test Data Preparation ✅
- Applied identical feature engineering to test data
- Generated predictions for all 30,789 test sessions
- Applied inverse log transformation to predictions

### 5. Submission Generation ✅
- Created submission.csv in required format
- All sessions from sample_submission.csv covered
- No missing or extra sessions

### 6. Evaluation & Debugging ✅
- Feature importance analysis
- Cross-validation performance metrics
- Distribution comparison plots
- Comprehensive testing suite

## 📈 Key Results

### Model Performance
- **Cross-validation RMSE**: 19.66 ± 7.41
- **Best fold**: 15.64 RMSE
- **Worst fold**: 25.69 RMSE
- **Coefficient of variation**: 18.86%

### Feature Importance (Top 5)
1. **num_purchases** (108,743) - Most predictive feature
2. **num_add_cart** (22,352) - Cart interactions matter
3. **hour_of_day** (10,660) - Time patterns important
4. **has_purchase** (9,266) - Purchase flag highly predictive
5. **has_add_cart** (4,812) - Cart interaction flag

### Prediction Statistics
- **Mean**: 40.78 (vs 42.20 in training)
- **Median**: 26.77 (vs 30.75 in training)
- **Range**: 8.18 - 894.01
- **Skewness**: 4.33 (reduced from 7.99 in training)

## 🔧 Files Description

- **`ml_pipeline.py`**: Main pipeline implementation
- **`analysis_summary.py`**: Detailed analysis and insights
- **`test_pipeline.py`**: Test suite for validation
- **`submission.csv`**: Final submission file
- **`feature_importance.png`**: Feature importance visualization
- **`prediction_distribution_comparison.png`**: Distribution comparison plot

## 🚀 How to Run

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the complete pipeline**:
   ```bash
   python3 ml_pipeline.py
   ```

3. **Run additional analysis**:
   ```bash
   python3 analysis_summary.py
   ```

4. **Run tests**:
   ```bash
   python3 test_pipeline.py
   ```

## 💡 Key Insights

### Business Insights
- Sessions with purchases have significantly higher values
- Time of day and day of week affect session values
- Users who interact with more products tend to have higher session values
- Cart abandonment patterns are predictive

### Technical Insights
- Purchase behavior is the strongest predictor
- Cart interactions are highly predictive
- Time patterns matter significantly
- Product diversity indicates higher-value sessions
- Log transformation was crucial for handling skewed target

## 🔮 Potential Improvements

1. **Hyperparameter Tuning**: Use Optuna for systematic optimization
2. **Ensemble Methods**: Combine LightGBM, XGBoost, and CatBoost
3. **Advanced Features**: User clustering, product embeddings
4. **Time Series Features**: More sophisticated temporal patterns
5. **External Data**: Product categories, seasonal patterns

## 📊 Model Validation

- ✅ All tests passed
- ✅ Submission format validated
- ✅ Performance metrics reasonable
- ✅ Feature engineering working correctly
- ✅ No data leakage detected

## 🏆 Competition Ready

The pipeline is fully functional and ready for the BTK Datathon 2025 submission. The `submission.csv` file contains predictions for all required sessions in the correct format.
