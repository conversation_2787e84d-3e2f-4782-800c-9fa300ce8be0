#!/usr/bin/env python3
"""
Test script for the E-commerce Session Value Prediction Pipeline
BTK Datathon 2025

This script runs basic tests to validate the pipeline functionality.
"""

import pandas as pd
import numpy as np
import os

def test_data_loading():
    """Test that data files exist and can be loaded"""
    print("Testing data loading...")
    
    # Check if files exist
    required_files = ['train.csv', 'test.csv', 'sample_submission.csv']
    for file in required_files:
        assert os.path.exists(file), f"Required file {file} not found"
    
    # Load data
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_sub = pd.read_csv('sample_submission.csv')
    
    # Basic checks
    assert len(train_df) > 0, "Training data is empty"
    assert len(test_df) > 0, "Test data is empty"
    assert len(sample_sub) > 0, "Sample submission is empty"
    
    # Check columns
    expected_train_cols = ['event_time', 'event_type', 'product_id', 'category_id', 'user_id', 'user_session', 'session_value']
    expected_test_cols = ['event_time', 'event_type', 'product_id', 'category_id', 'user_id', 'user_session']
    
    assert list(train_df.columns) == expected_train_cols, f"Training columns mismatch: {list(train_df.columns)}"
    assert list(test_df.columns) == expected_test_cols, f"Test columns mismatch: {list(test_df.columns)}"
    
    print("✓ Data loading tests passed")
    return train_df, test_df, sample_sub

def test_feature_engineering():
    """Test feature engineering functionality"""
    print("Testing feature engineering...")
    
    from ml_pipeline import EcommerceSessionPredictor
    
    # Create a small sample of data for testing
    sample_data = pd.DataFrame({
        'event_time': ['2025-06-19 10:23:07+00:00', '2025-06-19 10:24:07+00:00', '2025-06-19 10:25:07+00:00'],
        'event_type': ['VIEW', 'ADD_CART', 'BUY'],
        'product_id': ['PROD_001', 'PROD_001', 'PROD_001'],
        'category_id': ['CAT_001', 'CAT_001', 'CAT_001'],
        'user_id': ['USER_001', 'USER_001', 'USER_001'],
        'user_session': ['SESSION_001', 'SESSION_001', 'SESSION_001'],
        'session_value': [100.0, 100.0, 100.0]
    })
    
    # Convert datetime
    sample_data['event_time'] = pd.to_datetime(sample_data['event_time'])
    
    # Initialize predictor and test feature engineering
    predictor = EcommerceSessionPredictor()
    features = predictor.engineer_features(sample_data, is_train=True)
    
    # Check that we get one session
    assert len(features) == 1, f"Expected 1 session, got {len(features)}"
    
    # Check key features
    session = features.iloc[0]
    assert session['total_events'] == 3, f"Expected 3 events, got {session['total_events']}"
    assert session['num_views'] == 1, f"Expected 1 view, got {session['num_views']}"
    assert session['num_add_cart'] == 1, f"Expected 1 add_cart, got {session['num_add_cart']}"
    assert session['num_purchases'] == 1, f"Expected 1 purchase, got {session['num_purchases']}"
    assert session['has_purchase'] == 1, f"Expected has_purchase=1, got {session['has_purchase']}"
    assert session['session_value'] == 100.0, f"Expected session_value=100.0, got {session['session_value']}"
    
    print("✓ Feature engineering tests passed")

def test_submission_format():
    """Test that submission file has correct format"""
    print("Testing submission format...")
    
    # Check if submission exists
    assert os.path.exists('submission.csv'), "Submission file not found"
    
    # Load submission and sample
    submission = pd.read_csv('submission.csv')
    sample_sub = pd.read_csv('sample_submission.csv')
    
    # Check format
    assert list(submission.columns) == ['user_session', 'session_value'], f"Wrong columns: {list(submission.columns)}"
    assert len(submission) == len(sample_sub), f"Wrong number of rows: {len(submission)} vs {len(sample_sub)}"
    
    # Check that all sessions are covered
    missing_sessions = set(sample_sub['user_session']) - set(submission['user_session'])
    assert len(missing_sessions) == 0, f"Missing sessions: {len(missing_sessions)}"
    
    # Check for reasonable predictions
    predictions = submission['session_value']
    assert predictions.min() > 0, f"Negative predictions found: {predictions.min()}"
    assert predictions.max() < 10000, f"Unreasonably high predictions: {predictions.max()}"
    assert not predictions.isnull().any(), "NaN predictions found"
    
    print("✓ Submission format tests passed")

def test_model_performance():
    """Test that model performance is reasonable"""
    print("Testing model performance...")
    
    # Load training data to get baseline statistics
    train_df = pd.read_csv('train.csv')
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    # Load predictions
    submission = pd.read_csv('submission.csv')
    predictions = submission['session_value']
    
    # Check that prediction statistics are reasonable compared to training data
    train_mean = train_session_values.mean()
    pred_mean = predictions.mean()
    
    # Predictions should be in a reasonable range compared to training data
    assert 0.1 * train_mean < pred_mean < 10 * train_mean, f"Prediction mean {pred_mean:.2f} seems unreasonable vs training mean {train_mean:.2f}"
    
    # Check that predictions have reasonable variance
    pred_std = predictions.std()
    train_std = train_session_values.std()
    assert 0.1 * train_std < pred_std < 10 * train_std, f"Prediction std {pred_std:.2f} seems unreasonable vs training std {train_std:.2f}"
    
    print("✓ Model performance tests passed")

def run_all_tests():
    """Run all tests"""
    print("="*50)
    print("RUNNING PIPELINE TESTS")
    print("="*50)
    
    try:
        test_data_loading()
        test_feature_engineering()
        test_submission_format()
        test_model_performance()
        
        print("\n" + "="*50)
        print("ALL TESTS PASSED! ✅")
        print("="*50)
        print("The pipeline is working correctly and ready for submission.")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        return False
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    if not success:
        exit(1)
