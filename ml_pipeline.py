#!/usr/bin/env python3
"""
E-commerce Session Value Prediction Pipeline
BTK Datathon 2025

This script implements a complete ML pipeline to predict session values
from e-commerce interaction data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from sklearn.model_selection import GroupKFold, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import LabelEncoder
import lightgbm as lgb
import xgboost as xgb

# For hyperparameter tuning (we'll use basic grid search since optuna isn't in requirements)
from sklearn.model_selection import GridSearchCV

class EcommerceSessionPredictor:
    """Main class for e-commerce session value prediction"""
    
    def __init__(self):
        self.train_df = None
        self.test_df = None
        self.train_features = None
        self.test_features = None
        self.model = None
        self.feature_names = None
        
    def load_data(self):
        """Load and inspect the training and test data"""
        print("Loading data...")
        self.train_df = pd.read_csv('train.csv')
        self.test_df = pd.read_csv('test.csv')
        
        print(f"Train shape: {self.train_df.shape}")
        print(f"Test shape: {self.test_df.shape}")
        
        # Parse datetime
        self.train_df['event_time'] = pd.to_datetime(self.train_df['event_time'])
        self.test_df['event_time'] = pd.to_datetime(self.test_df['event_time'])
        
        return self
    
    def preprocess_data(self):
        """Data preprocessing and cleaning"""
        print("Preprocessing data...")
        
        # Check for missing values
        print("Missing values in train:", self.train_df.isnull().sum().sum())
        print("Missing values in test:", self.test_df.isnull().sum().sum())
        
        # Event type distribution
        print("\nEvent type distribution:")
        print(self.train_df['event_type'].value_counts())
        
        # Session value distribution
        print(f"\nSession value stats:")
        print(self.train_df['session_value'].describe())
        
        # Check for skewness
        skewness = self.train_df['session_value'].skew()
        print(f"Session value skewness: {skewness:.3f}")
        
        return self
    
    def engineer_features(self, df, is_train=True):
        """
        Engineer session-level features from event-level data
        
        Args:
            df: DataFrame with event-level data
            is_train: Whether this is training data (has session_value)
        
        Returns:
            DataFrame with session-level features
        """
        print(f"Engineering features for {'train' if is_train else 'test'} data...")
        
        # Group by session to create session-level features
        session_features = []
        
        for session_id, session_data in df.groupby('user_session'):
            features = {'user_session': session_id}
            
            # Basic counts
            features['total_events'] = len(session_data)
            features['num_views'] = (session_data['event_type'] == 'VIEW').sum()
            features['num_add_cart'] = (session_data['event_type'] == 'ADD_CART').sum()
            features['num_remove_cart'] = (session_data['event_type'] == 'REMOVE_CART').sum()
            features['num_purchases'] = (session_data['event_type'] == 'BUY').sum()
            
            # Unique counts
            features['unique_products'] = session_data['product_id'].nunique()
            features['unique_categories'] = session_data['category_id'].nunique()
            
            # Binary flags
            features['has_purchase'] = int(features['num_purchases'] > 0)
            features['has_view'] = int(features['num_views'] > 0)
            features['has_add_cart'] = int(features['num_add_cart'] > 0)
            features['has_remove_cart'] = int(features['num_remove_cart'] > 0)
            
            # Conversion metrics
            features['conversion_rate'] = features['num_purchases'] / max(features['num_views'], 1)
            features['cart_conversion_rate'] = features['num_purchases'] / max(features['num_add_cart'], 1)
            features['cart_abandonment_rate'] = features['num_remove_cart'] / max(features['num_add_cart'], 1)
            
            # Event type ratios
            features['view_ratio'] = features['num_views'] / features['total_events']
            features['add_cart_ratio'] = features['num_add_cart'] / features['total_events']
            features['remove_cart_ratio'] = features['num_remove_cart'] / features['total_events']
            features['purchase_ratio'] = features['num_purchases'] / features['total_events']
            
            # Product/Category diversity
            features['products_per_event'] = features['unique_products'] / features['total_events']
            features['categories_per_event'] = features['unique_categories'] / features['total_events']
            
            # Time-based features
            session_times = session_data['event_time'].sort_values()
            features['session_duration_minutes'] = (session_times.iloc[-1] - session_times.iloc[0]).total_seconds() / 60
            features['hour_of_day'] = session_times.iloc[0].hour
            features['day_of_week'] = session_times.iloc[0].dayofweek
            features['is_weekend'] = int(session_times.iloc[0].dayofweek >= 5)
            
            # Most frequent category and product
            most_freq_cat = session_data['category_id'].mode()
            most_freq_prod = session_data['product_id'].mode()
            features['most_freq_category'] = most_freq_cat.iloc[0] if len(most_freq_cat) > 0 else 'UNKNOWN'
            features['most_freq_product'] = most_freq_prod.iloc[0] if len(most_freq_prod) > 0 else 'UNKNOWN'
            
            # User information
            features['user_id'] = session_data['user_id'].iloc[0]
            
            # Target variable (only for training data)
            if is_train:
                features['session_value'] = session_data['session_value'].iloc[0]
            
            session_features.append(features)
        
        features_df = pd.DataFrame(session_features)
        
        # Encode categorical features
        categorical_features = ['most_freq_category', 'most_freq_product', 'user_id']
        for cat_feature in categorical_features:
            if cat_feature in features_df.columns:
                le = LabelEncoder()
                features_df[f'{cat_feature}_encoded'] = le.fit_transform(features_df[cat_feature].astype(str))
        
        print(f"Created {len(features_df)} session-level records with {len(features_df.columns)} features")
        return features_df
    
    def prepare_features(self):
        """Prepare features for both train and test sets"""
        print("Preparing features...")
        
        # Engineer features
        self.train_features = self.engineer_features(self.train_df, is_train=True)
        self.test_features = self.engineer_features(self.test_df, is_train=False)
        
        # Get feature columns (excluding target and identifiers)
        exclude_cols = ['user_session', 'session_value', 'most_freq_category', 'most_freq_product', 'user_id']
        self.feature_names = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"Using {len(self.feature_names)} features for modeling")
        print("Features:", self.feature_names)
        
        return self

    def train_model(self):
        """Train the regression model with cross-validation"""
        print("Training model...")

        # Prepare training data
        X_train = self.train_features[self.feature_names]
        y_train = self.train_features['session_value']

        print(f"Training data shape: {X_train.shape}")
        print(f"Target distribution: mean={y_train.mean():.2f}, std={y_train.std():.2f}")

        # Check if we should log-transform the target (if highly skewed)
        skewness = y_train.skew()
        print(f"Target skewness: {skewness:.3f}")

        use_log_transform = skewness > 2
        if use_log_transform:
            print("Applying log transformation to target variable")
            y_train_transformed = np.log1p(y_train)
        else:
            y_train_transformed = y_train

        # Use GroupKFold to avoid data leakage (group by user_id)
        user_groups = self.train_features['user_id_encoded']
        gkf = GroupKFold(n_splits=5)

        # Try LightGBM first
        print("Training LightGBM model...")

        # Basic hyperparameters for LightGBM
        lgb_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }

        # Cross-validation
        cv_scores = []
        feature_importance = np.zeros(len(self.feature_names))
        models = []

        for fold, (train_idx, val_idx) in enumerate(gkf.split(X_train, y_train_transformed, groups=user_groups)):
            print(f"Training fold {fold + 1}/5...")

            X_fold_train, X_fold_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
            y_fold_train, y_fold_val = y_train_transformed.iloc[train_idx], y_train_transformed.iloc[val_idx]

            # Create LightGBM datasets
            train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
            val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)

            # Train model
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
            )

            models.append(model)

            # Predict and evaluate
            val_pred = model.predict(X_fold_val)

            # Transform back if log-transformed
            if use_log_transform:
                val_pred = np.expm1(val_pred)
                y_fold_val_original = np.expm1(y_fold_val)
            else:
                y_fold_val_original = y_fold_val

            fold_rmse = np.sqrt(mean_squared_error(y_fold_val_original, val_pred))
            cv_scores.append(fold_rmse)

            # Accumulate feature importance
            feature_importance += model.feature_importance(importance_type='gain')

            print(f"Fold {fold + 1} RMSE: {fold_rmse:.4f}")

        print(f"Cross-validation RMSE: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores) * 2:.4f})")

        # Train final model on full dataset
        print("Training final model on full dataset...")
        train_data = lgb.Dataset(X_train, label=y_train_transformed)

        avg_num_trees = int(np.mean([m.num_trees() for m in models]))
        self.model = lgb.train(
            lgb_params,
            train_data,
            num_boost_round=avg_num_trees,
            callbacks=[lgb.log_evaluation(0)]
        )

        # Store feature importance and other info
        self.feature_importance = feature_importance / 5  # Average across folds
        self.use_log_transform = use_log_transform
        self.cv_scores = cv_scores

        return self

    def predict(self):
        """Generate predictions for test data"""
        print("Generating predictions...")

        # Prepare test features
        X_test = self.test_features[self.feature_names]

        # Make predictions
        test_pred = self.model.predict(X_test)

        # Transform back if log-transformed
        if self.use_log_transform:
            test_pred = np.expm1(test_pred)

        # Create submission dataframe
        submission = pd.DataFrame({
            'user_session': self.test_features['user_session'],
            'session_value': test_pred
        })

        print(f"Generated predictions for {len(submission)} sessions")
        print(f"Prediction stats: mean={test_pred.mean():.2f}, std={test_pred.std():.2f}")

        return submission

    def analyze_results(self):
        """Analyze model results and feature importance"""
        print("Analyzing results...")

        # Feature importance plot
        feature_imp_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.feature_importance
        }).sort_values('importance', ascending=False)

        print("\nTop 15 Most Important Features:")
        print(feature_imp_df.head(15))

        # Plot feature importance
        plt.figure(figsize=(10, 8))
        sns.barplot(data=feature_imp_df.head(15), x='importance', y='feature')
        plt.title('Top 15 Feature Importance')
        plt.xlabel('Importance')
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()  # Close instead of show to avoid hanging

        # Cross-validation results
        print(f"\nCross-validation results:")
        print(f"Mean RMSE: {np.mean(self.cv_scores):.4f}")
        print(f"Std RMSE: {np.std(self.cv_scores):.4f}")
        print(f"Individual fold scores: {[f'{score:.4f}' for score in self.cv_scores]}")

        return self

    def run_pipeline(self):
        """Run the complete ML pipeline"""
        print("="*50)
        print("E-COMMERCE SESSION VALUE PREDICTION PIPELINE")
        print("="*50)

        # Execute pipeline steps
        self.load_data()
        self.preprocess_data()
        self.prepare_features()
        self.train_model()

        # Generate predictions
        submission = self.predict()

        # Analyze results
        self.analyze_results()

        return submission


def main():
    """Main function to run the pipeline"""
    # Initialize predictor
    predictor = EcommerceSessionPredictor()

    # Run pipeline
    submission = predictor.run_pipeline()

    # Save submission
    submission.to_csv('submission.csv', index=False)
    print(f"\nSubmission saved to 'submission.csv'")
    print(f"Submission shape: {submission.shape}")

    # Verify submission format matches sample
    sample_submission = pd.read_csv('sample_submission.csv')
    print(f"Sample submission shape: {sample_submission.shape}")

    # Check if all sessions are covered
    missing_sessions = set(sample_submission['user_session']) - set(submission['user_session'])
    if missing_sessions:
        print(f"WARNING: {len(missing_sessions)} sessions missing from submission")
    else:
        print("✓ All sessions covered in submission")

    print("\nPipeline completed successfully!")


if __name__ == "__main__":
    main()
