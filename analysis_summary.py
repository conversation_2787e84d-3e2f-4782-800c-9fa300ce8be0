#!/usr/bin/env python3
"""
Analysis Summary for E-commerce Session Value Prediction
BTK Datathon 2025

This script provides additional analysis and insights from the ML pipeline results.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_submission():
    """Analyze the submission results"""
    print("="*60)
    print("SUBMISSION ANALYSIS")
    print("="*60)
    
    # Load data
    submission = pd.read_csv('submission.csv')
    sample_submission = pd.read_csv('sample_submission.csv')
    train_df = pd.read_csv('train.csv')
    
    print(f"Submission shape: {submission.shape}")
    print(f"Sample submission shape: {sample_submission.shape}")
    
    # Check submission format
    print(f"\nSubmission columns: {list(submission.columns)}")
    print(f"Sample submission columns: {list(sample_submission.columns)}")
    
    # Prediction statistics
    predictions = submission['session_value']
    print(f"\nPrediction Statistics:")
    print(f"Mean: {predictions.mean():.2f}")
    print(f"Median: {predictions.median():.2f}")
    print(f"Std: {predictions.std():.2f}")
    print(f"Min: {predictions.min():.2f}")
    print(f"Max: {predictions.max():.2f}")
    print(f"Skewness: {predictions.skew():.3f}")
    
    # Compare with training data
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    print(f"\nTraining Data Session Values:")
    print(f"Mean: {train_session_values.mean():.2f}")
    print(f"Median: {train_session_values.median():.2f}")
    print(f"Std: {train_session_values.std():.2f}")
    print(f"Min: {train_session_values.min():.2f}")
    print(f"Max: {train_session_values.max():.2f}")
    print(f"Skewness: {train_session_values.skew():.3f}")
    
    # Check for missing sessions
    missing_sessions = set(sample_submission['user_session']) - set(submission['user_session'])
    if missing_sessions:
        print(f"\nWARNING: {len(missing_sessions)} sessions missing from submission")
        print(f"First few missing: {list(missing_sessions)[:5]}")
    else:
        print(f"\n✓ All {len(sample_submission)} sessions covered in submission")
    
    # Check for extra sessions
    extra_sessions = set(submission['user_session']) - set(sample_submission['user_session'])
    if extra_sessions:
        print(f"\nWARNING: {len(extra_sessions)} extra sessions in submission")
        print(f"First few extra: {list(extra_sessions)[:5]}")
    else:
        print(f"✓ No extra sessions in submission")
    
    return submission, train_session_values

def analyze_feature_importance():
    """Analyze feature importance from the model"""
    print("\n" + "="*60)
    print("FEATURE IMPORTANCE ANALYSIS")
    print("="*60)
    
    # This would require loading the trained model, but we can analyze from the output
    print("Key insights from feature importance:")
    print("1. 'num_purchases' is by far the most important feature")
    print("2. 'num_add_cart' is the second most important")
    print("3. Time-based features (hour_of_day, day_of_week) are significant")
    print("4. Purchase-related flags (has_purchase, has_add_cart) are important")
    print("5. Product diversity (unique_products, unique_categories) matters")
    print("6. User behavior patterns (view_ratio, remove_cart_ratio) are relevant")

def analyze_model_performance():
    """Analyze model performance"""
    print("\n" + "="*60)
    print("MODEL PERFORMANCE ANALYSIS")
    print("="*60)
    
    # Cross-validation results from the pipeline output
    cv_scores = [22.1184, 17.5526, 15.6354, 25.6906, 17.2867]
    mean_rmse = np.mean(cv_scores)
    std_rmse = np.std(cv_scores)
    
    print(f"Cross-validation RMSE: {mean_rmse:.4f} (+/- {std_rmse * 2:.4f})")
    print(f"Individual fold scores: {cv_scores}")
    print(f"Best fold: {min(cv_scores):.4f}")
    print(f"Worst fold: {max(cv_scores):.4f}")
    print(f"Coefficient of variation: {(std_rmse / mean_rmse) * 100:.2f}%")
    
    print("\nModel insights:")
    print("- Log transformation was applied due to high target skewness (7.99)")
    print("- GroupKFold was used to prevent data leakage by user")
    print("- Early stopping prevented overfitting")
    print("- LightGBM performed well with default hyperparameters")

def create_prediction_distribution_plot():
    """Create a plot comparing prediction and training distributions"""
    print("\n" + "="*60)
    print("CREATING DISTRIBUTION COMPARISON PLOT")
    print("="*60)
    
    submission = pd.read_csv('submission.csv')
    train_df = pd.read_csv('train.csv')
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    plt.figure(figsize=(12, 6))
    
    # Plot distributions
    plt.subplot(1, 2, 1)
    plt.hist(train_session_values, bins=50, alpha=0.7, label='Training Data', density=True)
    plt.hist(submission['session_value'], bins=50, alpha=0.7, label='Predictions', density=True)
    plt.xlabel('Session Value')
    plt.ylabel('Density')
    plt.title('Distribution Comparison')
    plt.legend()
    plt.yscale('log')
    
    # Plot log-scale distributions
    plt.subplot(1, 2, 2)
    plt.hist(np.log1p(train_session_values), bins=50, alpha=0.7, label='Training Data (log)', density=True)
    plt.hist(np.log1p(submission['session_value']), bins=50, alpha=0.7, label='Predictions (log)', density=True)
    plt.xlabel('Log(Session Value + 1)')
    plt.ylabel('Density')
    plt.title('Log-Scale Distribution Comparison')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('prediction_distribution_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Distribution comparison plot saved as 'prediction_distribution_comparison.png'")

def generate_insights():
    """Generate key insights and recommendations"""
    print("\n" + "="*60)
    print("KEY INSIGHTS AND RECOMMENDATIONS")
    print("="*60)
    
    print("🎯 MODEL PERFORMANCE:")
    print("- Cross-validation RMSE: 19.66 (reasonable for this problem)")
    print("- Model successfully handles highly skewed target variable")
    print("- Feature engineering created meaningful predictive signals")
    
    print("\n📊 FEATURE INSIGHTS:")
    print("- Purchase behavior is the strongest predictor (num_purchases, has_purchase)")
    print("- Cart interactions are highly predictive (num_add_cart, has_add_cart)")
    print("- Time patterns matter (hour_of_day, day_of_week)")
    print("- Product diversity indicates higher-value sessions")
    
    print("\n🔧 POTENTIAL IMPROVEMENTS:")
    print("- Hyperparameter tuning with Optuna could improve performance")
    print("- Ensemble methods (combining LightGBM, XGBoost, CatBoost)")
    print("- More sophisticated time-based features")
    print("- User clustering or segmentation features")
    print("- Product/category embeddings")
    
    print("\n📈 BUSINESS INSIGHTS:")
    print("- Sessions with purchases have significantly higher values")
    print("- Time of day and day of week affect session values")
    print("- Users who interact with more products tend to have higher session values")
    print("- Cart abandonment patterns are predictive")

def main():
    """Main analysis function"""
    # Run all analyses
    submission, train_values = analyze_submission()
    analyze_feature_importance()
    analyze_model_performance()
    create_prediction_distribution_plot()
    generate_insights()
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE!")
    print("="*60)
    print("Files generated:")
    print("- submission.csv (main submission file)")
    print("- feature_importance.png (feature importance plot)")
    print("- prediction_distribution_comparison.png (distribution comparison)")
    print("\nThe submission is ready for the BTK Datathon 2025!")

if __name__ == "__main__":
    main()
